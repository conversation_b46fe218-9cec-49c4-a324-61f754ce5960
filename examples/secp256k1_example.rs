use secp256k1::{Secp256k1, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Message};
use secp256k1::ecdsa::Signature;
use sha2::{Sha256, Digest};
use rand::RngCore;

// Simplified secp256k1 key derivation using direct query hashing
#[derive(Clone)]
struct ExtendedPrivateKey {
    secret_key: SecretKey,
}

#[derive(Clone)]
struct ExtendedPublicKey {
    public_key: PublicKey,
}

impl ExtendedPrivateKey {
    fn generate() -> Self {
        let mut rng = rand::thread_rng();
        
        // Generate random seed
        let mut seed = [0u8; 32];
        rng.fill_bytes(&mut seed);
        
        Self::from_seed(&seed)
    }
    
    fn from_seed(seed: &[u8]) -> Self {
        // Hash seed to get key material
        let mut hasher = Sha256::new();
        hasher.update(b"secp256k1 seed");
        hasher.update(seed);
        let key_material = hasher.finalize();

        // Create secret key from first 32 bytes
        let secret_key = SecretKey::from_slice(&key_material[..32])
            .expect("32 bytes, within curve order");

        ExtendedPrivateKey { secret_key }
    }
    
    fn derive_child(&self, query: &str) -> Option<ExtendedPrivateKey> {
        // Hash query to get deterministic scalar
        let mut hasher = Sha256::new();
        hasher.update(b"query:");
        hasher.update(query.as_bytes());
        let query_hash = hasher.finalize();

        // Parse hash as scalar (this will always succeed since we mod by curve order)
        let query_scalar = SecretKey::from_slice(&query_hash).ok()?;

        // Child key = (parent_key + query_scalar) mod n
        let child_key = self.secret_key.add_tweak(&query_scalar.into()).ok()?;

        Some(ExtendedPrivateKey {
            secret_key: child_key,
        })
    }
    
    fn public_key(&self) -> PublicKey {
        let secp = Secp256k1::new();
        PublicKey::from_secret_key(&secp, &self.secret_key)
    }
    

    
    fn sign(&self, message: &[u8]) -> Signature {
        let secp = Secp256k1::new();
        let message_hash = {
            let mut hasher = Sha256::new();
            hasher.update(message);
            hasher.finalize()
        };
        let msg = Message::from_digest_slice(&message_hash).expect("32 bytes");
        secp.sign_ecdsa(&msg, &self.secret_key)
    }
}

impl ExtendedPublicKey {
    fn derive_child(&self, query: &str) -> Option<ExtendedPublicKey> {
        let secp = Secp256k1::new();

        // Hash query to get deterministic scalar (same as private key derivation)
        let mut hasher = Sha256::new();
        hasher.update(b"query:");
        hasher.update(query.as_bytes());
        let query_hash = hasher.finalize();

        // Parse hash as scalar
        let query_scalar = SecretKey::from_slice(&query_hash).ok()?;
        let query_point = PublicKey::from_secret_key(&secp, &query_scalar);

        // Child public key = parent_public_key + query_scalar*G
        let child_public_key = self.public_key.combine(&query_point).ok()?;

        Some(ExtendedPublicKey {
            public_key: child_public_key,
        })
    }
}

// Direct query-based key derivation (no indexing needed)
fn derive_child_private(root: &ExtendedPrivateKey, query: &str) -> Option<ExtendedPrivateKey> {
    root.derive_child(query)
}

fn derive_child_public(root: &ExtendedPublicKey, query: &str) -> Option<ExtendedPublicKey> {
    root.derive_child(query)
}

// Publisher: has root private key, can derive signing keys
pub struct Publisher {
    root_keypair: ExtendedPrivateKey,
}

impl Publisher {
    pub fn new() -> Self {
        let root_keypair = ExtendedPrivateKey::generate();
        Publisher { root_keypair }
    }

    pub fn from_seed(seed: &[u8]) -> Self {
        let root_keypair = ExtendedPrivateKey::from_seed(seed);
        Publisher { root_keypair }
    }
    
    pub fn root_public_key(&self) -> PublicKey {
        self.root_keypair.public_key()
    }
    
    pub fn sign_data(&self, query: &str, data: &str) -> Option<(PublicKey, Signature)> {
        // Derive child key for this query
        let child_keypair = derive_child_private(&self.root_keypair, query)?;

        let child_public = child_keypair.public_key();
        let signature = child_keypair.sign(data.as_bytes());

        Some((child_public, signature))
    }
}

// Client: has only root public key, can derive verification keys
pub struct Client {
    root_public: ExtendedPublicKey,
}

impl Client {
    pub fn new(root_public_key: PublicKey) -> Self {
        let root_public = ExtendedPublicKey {
            public_key: root_public_key,
        };

        Client { root_public }
    }
    
    pub fn derive_query_public_key(&self, query: &str) -> Option<PublicKey> {
        let child_public = derive_child_public(&self.root_public, query)?;
        Some(child_public.public_key)
    }
    
    pub fn verify_data(&self, query: &str, data: &str, signature: &Signature, expected_public_key: &PublicKey) -> bool {
        // Derive the public key for this query
        if let Some(derived_public_key) = self.derive_query_public_key(query) {
            // Check if derived key matches expected
            if derived_public_key != *expected_public_key {
                return false;
            }

            // Verify signature
            let secp = Secp256k1::new();
            let message_hash = {
                let mut hasher = Sha256::new();
                hasher.update(data.as_bytes());
                hasher.finalize()
            };
            let msg = Message::from_digest_slice(&message_hash).expect("32 bytes");
            secp.verify_ecdsa(&msg, signature, &derived_public_key).is_ok()
        } else {
            false
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== secp256k1 Non-Hardened Key Derivation Example ===\n");
    
    // Publisher workflow
    println!("=== Publisher Workflow ===");
    let publisher = Publisher::new();
    let root_pub = publisher.root_public_key();
    
    println!("Root public key: {}", hex::encode(root_pub.serialize()));
    
    // Sign some data
    println!("\n=== Signing Data ===");
    let (hello_pubkey, hello_sig) = publisher.sign_data("hello", "world")
        .ok_or("Failed to derive key for 'hello'")?;
    let (foo_pubkey, foo_sig) = publisher.sign_data("foo", "bar")
        .ok_or("Failed to derive key for 'foo'")?;

    println!("Signed 'world' for query 'hello':");
    println!("  - Child public key: {}", hex::encode(hello_pubkey.serialize()));
    println!("  - Signature: {}", hex::encode(hello_sig.serialize_compact()));

    println!("Signed 'bar' for query 'foo':");
    println!("  - Child public key: {}", hex::encode(foo_pubkey.serialize()));
    println!("  - Signature: {}", hex::encode(foo_sig.serialize_compact()));
    
    // Client workflow
    println!("\n=== Client Workflow ===");

    // Client gets only the root public key from publisher
    let client = Client::new(root_pub);
    
    // Client derives the same public keys
    println!("\n=== Key Derivation Verification ===");

    // Test "hello" query
    if let Some(client_hello_pubkey) = client.derive_query_public_key("hello") {
        println!("Client derived 'hello' key: {}", hex::encode(client_hello_pubkey.serialize()));

        // Verify they match
        if client_hello_pubkey == hello_pubkey {
            println!("✅ SUCCESS: Client and publisher derived the SAME key for 'hello'!");
        } else {
            println!("❌ ERROR: Client and publisher derived DIFFERENT keys for 'hello'!");
        }

        // Verify signature
        if client.verify_data("hello", "world", &hello_sig, &hello_pubkey) {
            println!("✅ SUCCESS: Client verified signature for 'hello' -> 'world'!");
        } else {
            println!("❌ ERROR: Client failed to verify signature for 'hello'!");
        }
    }
    
    // Test "foo" query
    if let Some(client_foo_pubkey) = client.derive_query_public_key("foo") {
        println!("\nClient derived 'foo' key: {}", hex::encode(client_foo_pubkey.serialize()));

        // Verify they match
        if client_foo_pubkey == foo_pubkey {
            println!("✅ SUCCESS: Client and publisher derived the SAME key for 'foo'!");
        } else {
            println!("❌ ERROR: Client and publisher derived DIFFERENT keys for 'foo'!");
        }

        // Verify signature
        if client.verify_data("foo", "bar", &foo_sig, &foo_pubkey) {
            println!("✅ SUCCESS: Client verified signature for 'foo' -> 'bar'!");
        } else {
            println!("❌ ERROR: Client failed to verify signature for 'foo'!");
        }
    }
    
    println!("\n=== Summary ===");
    println!("✅ Publisher can derive signing keys from root private key");
    println!("✅ Client can derive same public keys from root public key only");
    println!("✅ Client never sees any private keys");
    println!("✅ Both derive identical keys for same queries");
    println!("✅ Direct query hashing - no indexing complexity needed!");
    println!("✅ secp256k1 supports non-hardened derivation natively");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_hashing_deterministic() {
        let query = "test_query";

        // Hash the same query twice
        let mut hasher1 = Sha256::new();
        hasher1.update(b"query:");
        hasher1.update(query.as_bytes());
        let hash1 = hasher1.finalize();

        let mut hasher2 = Sha256::new();
        hasher2.update(b"query:");
        hasher2.update(query.as_bytes());
        let hash2 = hasher2.finalize();

        assert_eq!(hash1, hash2); // Deterministic
    }

    #[test]
    fn test_key_derivation_consistency() {
        let root = ExtendedPrivateKey::generate();
        let root_pub = root.public();

        let query = "test_query";

        // Test private key derivation
        let child_priv = derive_child_private(&root, query).unwrap();

        // Test public key derivation
        let child_pub = derive_child_public(&root_pub, query).unwrap();

        assert_eq!(child_priv.public_key(), child_pub.public_key);
    }

    #[test]
    fn test_signature_verification() {
        let publisher = Publisher::new();
        let root_pub = publisher.root_public_key();
        let client = Client::new(root_pub);

        let query = "test";
        let data = "test_data";

        // Publisher signs
        let (pubkey, signature) = publisher.sign_data(query, data).unwrap();

        // Client verifies
        assert!(client.verify_data(query, data, &signature, &pubkey));
    }
}
